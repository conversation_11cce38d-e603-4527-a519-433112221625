# This file tracks properties of this Flutter project.
# Used by Flutter tool to assess capabilities and perform upgrades etc.
#
# This file should be version controlled.

version:
  revision: b8f7f1f9869bb2d116aa6a70dbeac61000b52849
  channel: stable

project_type: app

# Tracks metadata for the flutter migrate command
migration:
  platforms:
    - platform: root
      create_revision: b8f7f1f9869bb2d116aa6a70dbeac61000b52849
      base_revision: b8f7f1f9869bb2d116aa6a70dbeac61000b52849
    - platform: linux
      create_revision: b8f7f1f9869bb2d116aa6a70dbeac61000b52849
      base_revision: b8f7f1f9869bb2d116aa6a70dbeac61000b52849
    - platform: macos
      create_revision: b8f7f1f9869bb2d116aa6a70dbeac61000b52849
      base_revision: b8f7f1f9869bb2d116aa6a70dbeac61000b52849
    - platform: windows
      create_revision: b8f7f1f9869bb2d116aa6a70dbeac61000b52849
      base_revision: b8f7f1f9869bb2d116aa6a70dbeac61000b52849

  # User provided section

  # List of Local paths (relative to this file) that should be
  # ignored by the migrate tool.
  #
  # Files that are not part of the templates will be ignored by default.
  unmanaged_files:
    - 'lib/main.dart'
    - 'ios/Runner.xcodeproj/project.pbxproj'
