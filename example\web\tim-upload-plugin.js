var global,factory;global=this,factory=function(){function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function o(e,t,o){return t&&n(e.prototype,t),o&&n(e,o),e}function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function i(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n,o,r={},a=Object.keys(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}var f="undefined"!=typeof wx&&"function"==typeof wx.getSystemInfoSync&&Boolean(wx.getSystemInfoSync().fontSizeSetting),u="undefined"!=typeof qq&&"function"==typeof qq.getSystemInfoSync&&Boolean(qq.getSystemInfoSync().fontSizeSetting),l="undefined"!=typeof tt&&"function"==typeof tt.getSystemInfoSync&&Boolean(tt.getSystemInfoSync().fontSizeSetting),c="undefined"!=typeof swan&&"function"==typeof swan.getSystemInfoSync&&Boolean(swan.getSystemInfoSync().fontSizeSetting),y="undefined"!=typeof my&&"function"==typeof my.getSystemInfoSync&&Boolean(my.getSystemInfoSync().fontSizeSetting),d="undefined"!=typeof uni&&"undefined"==typeof window,p=f||u||l||c||y||d,h=u?qq:l?tt:c?swan:y?my:f?wx:d?uni:{},g=function(t){if("object"!==e(t)||null===t)return!1;var n=Object.getPrototypeOf(t);if(null===n)return!0;for(var o=n;null!==Object.getPrototypeOf(o);)o=Object.getPrototypeOf(o);return n===o};function m(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(g(e)){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}return!1}var b=function(){function e(){t(this,e)}return o(e,[{key:"request",value:function(e,t){var n=this,o=e.downloadUrl||"",r=(e.method||"PUT").toUpperCase(),a=e.url;if(e.qs){var s=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"&",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"=";return m(e)?"":g(e)?Object.keys(e).map((function(o){var r=encodeURIComponent(o)+n;return Array.isArray(e[o])?e[o].map((function(e){return r+encodeURIComponent(e)})).join(t):r+encodeURIComponent(e[o])})).filter(Boolean).join(t):void 0}(e.qs);s&&(a+="".concat(-1===a.indexOf("?")?"?":"&").concat(s))}var i=new XMLHttpRequest;i.open(r,a,!0),i.responseType=e.dataType||"text";var f=e.headers||{};if(!m(f))for(var u in f)f.hasOwnProperty(u)&&"content-length"!==u.toLowerCase()&&"user-agent"!==u.toLowerCase()&&"origin"!==u.toLowerCase()&&"host"!==u.toLowerCase()&&i.setRequestHeader(u,f[u]);return i.onload=function(){t(null,n._xhrRes(i,n._xhrBody(i,o)))},i.onerror=function(e){var o=n._xhrBody(i);if(o)t(e,n._xhrRes(i,o));else{var r=i.statusText;r||0!==i.status||(r="CORS blocked or network error"),t(r,n._xhrRes(i,o))}},e.onProgress&&i.upload&&(i.upload.onprogress=function(t){var n=t.total,o=t.loaded,r=Math.floor(100*o/n);e.onProgress({total:n,loaded:o,percent:(r>=100?100:r)/100})}),i.send(e.resources),i}},{key:"_xhrRes",value:function(e,t){var n={};return e.getAllResponseHeaders().trim().split("\n").forEach((function(e){if(e){var t=e.indexOf(":"),o=e.substr(0,t).trim().toLowerCase(),r=e.substr(t+1).trim();n[o]=r}})),{statusCode:e.status,statusMessage:e.statusText,headers:n,data:t}}},{key:"_xhrBody",value:function(e,t){return 200===e.status&&t?{location:t}:{response:e.responseText}}}]),e}(),v=["unknown","image","video","audio","log"],O=function(){function e(){t(this,e)}return o(e,[{key:"request",value:function(e,t){var n=this,o=e.resources,r=void 0===o?"":o,a=e.headers,f=void 0===a?{}:a,u=e.url,l=e.downloadUrl,c=void 0===l?"":l,d=null,p="",g=c.match(/^(https?:\/\/[^/]+\/)([^/]*\/?)(.*)$/),m={url:u,header:f,name:"file",filePath:r,formData:{key:p=(p=decodeURIComponent(g[3])).indexOf("?")>-1?p.split("?")[0]:p,success_action_status:200,"Content-Type":""},timeout:e.timeout||3e5};if(y){var b=m;b.name,m=s(s({},i(b,["name"])),{},{fileName:"file",fileType:v[e.fileType]})}return(d=h.uploadFile(s(s({},m),{},{success:function(e){n._handleResponse({response:e,downloadUrl:c,callback:t})},fail:function(e){n._handleResponse({response:e,downloadUrl:c,callback:t})}}))).onProgressUpdate((function(t){e.onProgress&&e.onProgress({total:t.totalBytesExpectedToSend,loaded:t.totalBytesSent,percent:Math.floor(t.progress)/100})})),d}},{key:"_handleResponse",value:function(e){var t=e.downloadUrl,n=e.response,o=e.callback,r=n.header,a={};if(r)for(var i in r)r.hasOwnProperty(i)&&(a[i.toLowerCase()]=r[i]);var f=+n.statusCode;200===f?o(null,{statusCode:f,headers:a,data:s(s({},n.data),{},{location:t})}):o(n,{statusCode:f,headers:a,data:void 0})}}]),e}();return function(){function e(){t(this,e),console.log("TIMUploadPlugin.VERSION: ".concat("1.0.4")),this.retry=1,this.tryCount=0,this.systemClockOffset=0,this.httpRequest=p?new O:new b}return o(e,[{key:"uploadFile",value:function(e,t){var n=this;return this.httpRequest.request(e,(function(o,r){o&&n.tryCount<n.retry&&n.allowRetry(o)?(n.tryCount++,n.uploadFile(e,t)):(n.tryCount=0,t(o,r))}))}},{key:"allowRetry",value:function(e){var t=!1,n=!1;if(e){var o=e.headers&&(e.headers.date||e.headers.Date)||e.error&&e.error.ServerTime;try{var r=e.error&&e.error.Code,a=e.error&&e.error.Message;("RequestTimeTooSkewed"===r||"AccessDenied"===r&&"Request has expired"===a)&&(n=!0)}catch(f){}if(n&&o){var s=Date.now(),i=Date.parse(o);Math.abs(s+this.systemClockOffset-i)>=3e4&&(this.systemClockOffset=i-s,t=!0)}else 5===Math.floor(e.statusCode/100)&&(t=!0)}return t}}]),e}()},"object"==typeof exports&&"undefined"!=typeof module?module.exports=factory():"function"==typeof define&&define.amd?define(factory):(global=global||self).TIMUploadPlugin=factory();
