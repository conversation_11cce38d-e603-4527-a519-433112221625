{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "audio_session", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audio_session-0.1.16\\\\", "native_build": true, "dependencies": []}, {"name": "better_player_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\better_player_plus-1.0.5\\\\", "native_build": true, "dependencies": ["wakelock_plus"]}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-10.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "fc_native_video_thumbnail", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fc_native_video_thumbnail-0.16.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_picker-5.3.2\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_image_compress_common", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_image_compress_common-1.0.6\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_native_splash", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_native_splash-2.4.0\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_record_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_plugin_record_plus-0.0.20\\\\", "native_build": true, "dependencies": []}, {"name": "fluttertoast", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fluttertoast-8.2.8\\\\", "native_build": true, "dependencies": []}, {"name": "image_cropper", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_cropper-9.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "image_gallery_saver_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_gallery_saver_plus-3.0.5\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_ios-0.8.8\\\\", "native_build": true, "dependencies": []}, {"name": "just_audio", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\just_audio-0.9.34\\\\", "native_build": true, "dependencies": ["audio_session"]}, {"name": "open_file", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\open_file-3.3.2\\\\", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "pasteboard", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\pasteboard-0.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.3.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_apple-9.1.3\\\\", "native_build": true, "dependencies": []}, {"name": "photo_manager", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\photo_manager-3.7.1\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.1+1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "tencent_calls_uikit", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tencent_calls_uikit-2.9.3\\\\", "native_build": true, "dependencies": ["tencent_cloud_uikit_core", "tencent_cloud_chat_sdk"]}, {"name": "tencent_cloud_chat_push", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tencent_cloud_chat_push-8.6.7019+1\\\\", "native_build": true, "dependencies": ["tencent_cloud_chat_sdk"]}, {"name": "tencent_cloud_chat_sdk", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tencent_cloud_chat_sdk-8.6.7019+2\\\\", "native_build": true, "dependencies": []}, {"name": "tencent_cloud_uikit_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tencent_cloud_uikit_core-1.7.4\\\\", "native_build": true, "dependencies": ["tencent_cloud_chat_sdk"]}, {"name": "url_launcher_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_ios-6.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_avfoundation-2.7.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.3.2\\\\", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "webview_flutter_wkwebview", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\webview_flutter_wkwebview-2.9.5\\\\", "native_build": true, "dependencies": []}], "android": [{"name": "audio_session", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audio_session-0.1.16\\\\", "native_build": true, "dependencies": []}, {"name": "better_player_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\better_player_plus-1.0.5\\\\", "native_build": true, "dependencies": ["wakelock_plus"]}, {"name": "desktop_drop", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\desktop_drop-0.4.4\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-10.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "fc_native_video_thumbnail", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fc_native_video_thumbnail-0.16.1\\\\", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_picker-5.3.2\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "flutter_image_compress_common", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_image_compress_common-1.0.6\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_native_splash", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_native_splash-2.4.0\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_plugin_android_lifecycle-2.0.15\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_record_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_plugin_record_plus-0.0.20\\\\", "native_build": true, "dependencies": []}, {"name": "fluttertoast", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fluttertoast-8.2.8\\\\", "native_build": true, "dependencies": []}, {"name": "image_cropper", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_cropper-9.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "image_gallery_saver_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_gallery_saver_plus-3.0.5\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_android-0.8.7+3\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "just_audio", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\just_audio-0.9.34\\\\", "native_build": true, "dependencies": ["audio_session"]}, {"name": "open_file", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\open_file-3.3.2\\\\", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.2\\\\", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_android-10.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "photo_manager", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\photo_manager-3.7.1\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.7\\\\", "native_build": true, "dependencies": []}, {"name": "sqflite_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_android-2.4.0\\\\", "native_build": true, "dependencies": []}, {"name": "tencent_calls_uikit", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tencent_calls_uikit-2.9.3\\\\", "native_build": true, "dependencies": ["tencent_cloud_uikit_core", "tencent_cloud_chat_sdk"]}, {"name": "tencent_cloud_chat_push", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tencent_cloud_chat_push-8.6.7019+1\\\\", "native_build": true, "dependencies": ["tencent_cloud_chat_sdk"]}, {"name": "tencent_cloud_chat_sdk", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tencent_cloud_chat_sdk-8.6.7019+2\\\\", "native_build": true, "dependencies": []}, {"name": "tencent_cloud_uikit_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tencent_cloud_uikit_core-1.7.4\\\\", "native_build": true, "dependencies": ["tencent_cloud_chat_sdk"]}, {"name": "url_launcher_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_android-6.0.36\\\\", "native_build": true, "dependencies": []}, {"name": "video_player_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_android-2.4.9\\\\", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.3.2\\\\", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "webview_flutter_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\webview_flutter_android-2.10.4\\\\", "native_build": true, "dependencies": []}], "macos": [{"name": "audio_session", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audio_session-0.1.16\\\\", "native_build": true, "dependencies": []}, {"name": "bitsdojo_window_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\bitsdojo_window_macos-0.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "desktop_drop", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\desktop_drop-0.4.4\\\\", "native_build": true, "dependencies": []}, {"name": "desktop_webview_window_for_is", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\desktop_webview_window_for_is-0.2.4\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-10.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "fc_native_video_thumbnail", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fc_native_video_thumbnail-0.16.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "file_selector_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_macos-0.9.3\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_image_compress_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_image_compress_macos-1.0.3\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_macos-0.2.1\\\\", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "just_audio", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\just_audio-0.9.34\\\\", "native_build": true, "dependencies": ["audio_session"]}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "pasteboard", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\pasteboard-0.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.3.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "photo_manager", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\photo_manager-3.7.1\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.1+1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "tencent_cloud_chat_sdk", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tencent_cloud_chat_sdk-8.6.7019+2\\\\", "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_macos-3.0.5\\\\", "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_avfoundation-2.7.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.3.2\\\\", "native_build": true, "dependencies": ["package_info_plus"]}], "linux": [{"name": "bitsdojo_window_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\bitsdojo_window_linux-0.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "desktop_drop", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\desktop_drop-0.4.4\\\\", "native_build": true, "dependencies": []}, {"name": "desktop_webview_window_for_is", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\desktop_webview_window_for_is-0.2.4\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-10.1.2\\\\", "native_build": false, "dependencies": []}, {"name": "file_selector_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_linux-0.9.2\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_linux-0.2.1\\\\", "native_build": false, "dependencies": ["file_selector_linux"]}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "pasteboard", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\pasteboard-0.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": []}, {"name": "shared_preferences_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "url_launcher_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_linux-3.0.5\\\\", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.3.2\\\\", "native_build": false, "dependencies": ["package_info_plus"]}], "windows": [{"name": "bitsdojo_window_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\bitsdojo_window_windows-0.1.6\\\\", "native_build": true, "dependencies": []}, {"name": "desktop_drop", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\desktop_drop-0.4.4\\\\", "native_build": true, "dependencies": []}, {"name": "desktop_webview_window_for_is", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\desktop_webview_window_for_is-0.2.4\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-10.1.2\\\\", "native_build": false, "dependencies": []}, {"name": "fc_native_video_thumbnail", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fc_native_video_thumbnail-0.16.1\\\\", "native_build": true, "dependencies": []}, {"name": "file_selector_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_windows-0.9.3\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_windows-0.2.1\\\\", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "pasteboard", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\pasteboard-0.2.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.2.1\\\\", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_windows-0.1.3\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "tencent_cloud_chat_sdk", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tencent_cloud_chat_sdk-8.6.7019+2\\\\", "native_build": true, "dependencies": []}, {"name": "url_launcher_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_windows-3.0.7\\\\", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.3.2\\\\", "native_build": false, "dependencies": ["package_info_plus"]}], "web": [{"name": "audio_session", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audio_session-0.1.16\\\\", "dependencies": []}, {"name": "desktop_drop", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\desktop_drop-0.4.4\\\\", "dependencies": []}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-10.1.2\\\\", "dependencies": []}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_picker-5.3.2\\\\", "dependencies": []}, {"name": "flutter_image_compress_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_image_compress_web-0.1.4+1\\\\", "dependencies": []}, {"name": "flutter_native_splash", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_native_splash-2.4.0\\\\", "dependencies": []}, {"name": "fluttertoast", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fluttertoast-8.2.8\\\\", "dependencies": []}, {"name": "image_cropper_for_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_cropper_for_web-6.1.0\\\\", "dependencies": []}, {"name": "image_picker_for_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_for_web-2.2.0\\\\", "dependencies": []}, {"name": "just_audio_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\just_audio_web-0.4.8\\\\", "dependencies": []}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.3.0\\\\", "dependencies": []}, {"name": "shared_preferences_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.3\\\\", "dependencies": []}, {"name": "tencent_cloud_chat_sdk", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tencent_cloud_chat_sdk-8.6.7019+2\\\\", "dependencies": []}, {"name": "url_launcher_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_web-2.0.18\\\\", "dependencies": []}, {"name": "video_player_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_web-2.3.5\\\\", "dependencies": []}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.3.2\\\\", "dependencies": ["package_info_plus"]}]}, "dependencyGraph": [{"name": "audio_session", "dependencies": []}, {"name": "better_player_plus", "dependencies": ["wakelock_plus", "path_provider"]}, {"name": "bitsdojo_window", "dependencies": ["bitsdojo_window_windows", "bitsdojo_window_macos", "bitsdojo_window_linux"]}, {"name": "bitsdojo_window_linux", "dependencies": []}, {"name": "bitsdojo_window_macos", "dependencies": []}, {"name": "bitsdojo_window_windows", "dependencies": []}, {"name": "desktop_drop", "dependencies": []}, {"name": "desktop_webview_window_for_is", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "fc_native_video_thumbnail", "dependencies": []}, {"name": "file_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "flutter_image_compress", "dependencies": ["flutter_image_compress_common", "flutter_image_compress_web", "flutter_image_compress_macos", "flutter_image_compress_ohos"]}, {"name": "flutter_image_compress_common", "dependencies": []}, {"name": "flutter_image_compress_macos", "dependencies": []}, {"name": "flutter_image_compress_ohos", "dependencies": []}, {"name": "flutter_image_compress_web", "dependencies": []}, {"name": "flutter_native_splash", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_plugin_record_plus", "dependencies": []}, {"name": "fluttertoast", "dependencies": []}, {"name": "image_cropper", "dependencies": ["image_cropper_for_web"]}, {"name": "image_cropper_for_web", "dependencies": []}, {"name": "image_gallery_saver_plus", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "just_audio", "dependencies": ["just_audio_web", "audio_session", "path_provider"]}, {"name": "just_audio_web", "dependencies": []}, {"name": "open_file", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "pasteboard", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "photo_manager", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "tencent_calls_uikit", "dependencies": ["path_provider", "shared_preferences", "tencent_cloud_uikit_core", "tencent_cloud_chat_sdk"]}, {"name": "tencent_cloud_chat_push", "dependencies": ["tencent_cloud_chat_sdk"]}, {"name": "tencent_cloud_chat_sdk", "dependencies": ["path_provider"]}, {"name": "tencent_cloud_uikit_core", "dependencies": ["tencent_cloud_chat_sdk"]}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}, {"name": "wakelock_plus", "dependencies": ["package_info_plus"]}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}], "date_created": "2025-07-16 13:40:45.678471", "version": "3.24.1", "swift_package_manager_enabled": false}