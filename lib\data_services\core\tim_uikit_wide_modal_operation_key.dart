
enum TUIKitWideModalOperationKey{

  /// You could use this value for your own Modal usage.
  custom,

  // The following values are used in TUIKit

  conversationSecondaryMenu,
  chooseCountry,
  beforeSendScreenShot,
  showUserProfileFromChat,
  addNewContact,
  showBlockedUsers,
  chooseContacts,
  addFriend,
  addGroup,
  chooseGroupType,
  settings,
  contactUs,
  aboutUs,
  showConditionsAndTerms,
  secondaryClickUserAvatar,
  forward,
  messageReadDetails,
  mergerMessageList,
  chooseMentionedMembers,
  chatHistory,
  groupAddOpt,
  setMute,
  setUnmute,
  setAdmins,
  deleteAdmin,
  groupMembersList,
  addGroupMembers,
  kickOffGroupMembers,
  confirmDeleteMessages,
  confirmClearChatHistory,
  confirmExitGroup,
  confirmDisbandGroup,
  confirmGeneral,
  unableToSendDueToFolders
}