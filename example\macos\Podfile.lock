PODS:
  - FlutterMacOS (1.0.0)
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - package_info_plus_macos (0.0.1):
    - FlutterMacOS
  - path_provider_macos (0.0.1):
    - FlutterMacOS
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - shared_preferences_macos (0.0.1):
    - FlutterMacOS
  - sqflite (0.0.2):
    - FlutterMacOS
    - FMDB (>= 2.7.5)
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - wakelock_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - FlutterMacOS (from `Flutter/ephemeral`)
  - package_info_plus_macos (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus_macos/macos`)
  - path_provider_macos (from `Flutter/ephemeral/.symlinks/plugins/path_provider_macos/macos`)
  - photo_manager (from `Flutter/ephemeral/.symlinks/plugins/photo_manager/macos`)
  - shared_preferences_macos (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_macos/macos`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - wakelock_macos (from `Flutter/ephemeral/.symlinks/plugins/wakelock_macos/macos`)

SPEC REPOS:
  trunk:
    - FMDB

EXTERNAL SOURCES:
  FlutterMacOS:
    :path: Flutter/ephemeral
  package_info_plus_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus_macos/macos
  path_provider_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_macos/macos
  photo_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/photo_manager/macos
  shared_preferences_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_macos/macos
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  wakelock_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_macos/macos

SPEC CHECKSUMS:
  FlutterMacOS: ae6af50a8ea7d6103d888583d46bd8328a7e9811
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  package_info_plus_macos: f010621b07802a241d96d01876d6705f15e77c1c
  path_provider_macos: 3c0c3b4b0d4a76d2bf989a913c2de869c5641a19
  photo_manager: 4f6810b7dfc4feb03b461ac1a70dacf91fba7604
  shared_preferences_macos: a64dc611287ed6cbe28fd1297898db1336975727
  sqflite: a5789cceda41d54d23f31d6de539d65bb14100ea
  url_launcher_macos: 597e05b8e514239626bcf4a850fcf9ef5c856ec3
  wakelock_macos: bc3f2a9bd8d2e6c89fee1e1822e7ddac3bd004a9

PODFILE CHECKSUM: 0d3963a09fc94f580682bd88480486da345dc3f0

COCOAPODS: 1.11.3
