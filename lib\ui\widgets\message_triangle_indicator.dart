import 'package:flutter/material.dart';

/// 聊天气泡的三角形指引标识
class MessageTriangleIndicator extends StatelessWidget {
  /// 是否来自自己
  final bool isFromSelf;
  
  /// 背景颜色
  final Color backgroundColor;
  
  /// 三角形大小
  final double size;

  const MessageTriangleIndicator({
    Key? key,
    required this.isFromSelf,
    required this.backgroundColor,
    this.size = 8.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: <PERSON><PERSON>(size, size),
      painter: _TrianglePainter(
        isFromSelf: isFromSelf,
        backgroundColor: backgroundColor,
      ),
    );
  }
}

/// 三角形绘制器
class _TrianglePainter extends CustomPainter {
  final bool isFromSelf;
  final Color backgroundColor;

  _TrianglePainter({
    required this.isFromSelf,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    final path = Path();
    
    if (isFromSelf) {
      // 发送方：右侧三角形，指向右边
      path.moveTo(0, 0);
      path.lineTo(size.width * 0.7, size.height * 0.5);
      path.lineTo(0, size.height);
      path.quadraticBezierTo(size.width * 0.2, size.height * 0.5, 0, 0);
    } else {
      // 接收方：左侧三角形，指向左边
      path.moveTo(size.width, 0);
      path.lineTo(size.width * 0.3, size.height * 0.5);
      path.lineTo(size.width, size.height);
      path.quadraticBezierTo(size.width * 0.8, size.height * 0.5, size.width, 0);
    }
    
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _TrianglePainter ||
        oldDelegate.isFromSelf != isFromSelf ||
        oldDelegate.backgroundColor != backgroundColor;
  }
}
